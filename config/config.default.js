'use strict';

module.exports = appInfo => {
  const config = exports = {};

  // use for cookie sign key, should change to your own and keep security
  config.keys = appInfo.name + '_{{keys}}';

  // add your config here
  config.middleware = [];

  // Sequelize 配置
  config.sequelize = {
    dialect: 'mysql',
    host: '*************',
    port: 3306,
    database: 'mydb1',
    username: 'root',
    password: 'Jtqtest123456!!',
    timezone: '+08:00',
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  };

  // 参数验证配置
  config.validate = {
    // 参数验证错误时的处理
    convert: true,
    // widelyUndefined: true,
  };

  // 安全配置
  config.security = {
    csrf: {
      enable: false, // 开发阶段关闭 CSRF，生产环境建议开启
    },
  };

  // CORS 配置（如果需要跨域）
  config.cors = {
    origin: '*',
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH',
  };

  // 错误处理配置
  config.onerror = {
    all(err, ctx) {
      // 在此处定义针对所有响应类型的错误处理方法
      // 注意，定义了 config.all 之后，其他错误处理方法不会再生效
      ctx.body = {
        success: false,
        message: err.message || '服务器内部错误',
        code: err.code || 500,
      };
      ctx.status = err.status || 500;
    },
    json(err, ctx) {
      // json hander
      ctx.body = {
        success: false,
        message: err.message || '服务器内部错误',
        code: err.code || 500,
      };
      ctx.status = err.status || 500;
    },
  };

  return config;
};
