'use strict';

const { factory } = require('factory-girl');

module.exports = app => {
  app.factory = factory;

  // Todo 测试数据工厂
  factory.define('todo', app.model.Todo, {
    title: factory.sequence('Todo.title', n => `测试任务 ${n}`),
    description: factory.sequence('Todo.description', n => `这是第 ${n} 个测试任务的描述`),
    completed: false,
    priority: 1,
    due_date: () => {
      const date = new Date();
      date.setDate(date.getDate() + 7); // 7天后
      return date;
    },
  });

  // 已完成的 Todo
  factory.define('completedTodo', app.model.Todo, {
    title: factory.sequence('CompletedTodo.title', n => `已完成任务 ${n}`),
    description: factory.sequence('CompletedTodo.description', n => `这是第 ${n} 个已完成任务的描述`),
    completed: true,
    priority: 2,
    due_date: () => {
      const date = new Date();
      date.setDate(date.getDate() - 1); // 昨天
      return date;
    },
  });

  // 高优先级 Todo
  factory.define('highPriorityTodo', app.model.Todo, {
    title: factory.sequence('HighPriorityTodo.title', n => `高优先级任务 ${n}`),
    description: factory.sequence('HighPriorityTodo.description', n => `这是第 ${n} 个高优先级任务的描述`),
    completed: false,
    priority: 3,
    due_date: () => {
      const date = new Date();
      date.setDate(date.getDate() + 1); // 明天
      return date;
    },
  });

  // 过期的 Todo
  factory.define('overdueTodo', app.model.Todo, {
    title: factory.sequence('OverdueTodo.title', n => `过期任务 ${n}`),
    description: factory.sequence('OverdueTodo.description', n => `这是第 ${n} 个过期任务的描述`),
    completed: false,
    priority: 2,
    due_date: () => {
      const date = new Date();
      date.setDate(date.getDate() - 3); // 3天前
      return date;
    },
  });

  // 无截止日期的 Todo
  factory.define('noDueDateTodo', app.model.Todo, {
    title: factory.sequence('NoDueDateTodo.title', n => `无截止日期任务 ${n}`),
    description: factory.sequence('NoDueDateTodo.description', n => `这是第 ${n} 个无截止日期任务的描述`),
    completed: false,
    priority: 1,
    due_date: null,
  });
};
