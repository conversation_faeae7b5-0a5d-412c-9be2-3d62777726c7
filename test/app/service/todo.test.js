'use strict';

const { app, assert } = require('egg-mock/bootstrap');

describe('test/app/service/todo.test.js', () => {
  let ctx;

  beforeEach(async () => {
    ctx = app.mockContext();
    // 清理测试数据
    await app.model.Todo.destroy({ where: {}, force: true });
  });

  afterEach(async () => {
    // 清理测试数据
    await app.model.Todo.destroy({ where: {}, force: true });
  });

  describe('list()', () => {
    it('should return empty list when no todos', async () => {
      const result = await ctx.service.todo.list();
      assert(result.list.length === 0);
      assert(result.total === 0);
      assert(result.page === 1);
      assert(result.limit === 10);
    });

    it('should return todos with pagination', async () => {
      // 创建测试数据
      await app.factory.createMany('todo', 15);
      
      const result = await ctx.service.todo.list({ page: 1, limit: 10 });
      assert(result.list.length === 10);
      assert(result.total === 15);
      assert(result.totalPages === 2);
    });

    it('should filter by completed status', async () => {
      await app.factory.create('todo');
      await app.factory.create('completedTodo');
      
      const completedResult = await ctx.service.todo.list({ completed: true });
      assert(completedResult.list.length === 1);
      assert(completedResult.list[0].completed === true);
      
      const incompleteResult = await ctx.service.todo.list({ completed: false });
      assert(incompleteResult.list.length === 1);
      assert(incompleteResult.list[0].completed === false);
    });

    it('should filter by priority', async () => {
      await app.factory.create('todo', { priority: 1 });
      await app.factory.create('highPriorityTodo', { priority: 3 });
      
      const result = await ctx.service.todo.list({ priority: 3 });
      assert(result.list.length === 1);
      assert(result.list[0].priority === 3);
    });
  });

  describe('findById()', () => {
    it('should return todo by id', async () => {
      const todo = await app.factory.create('todo');
      const result = await ctx.service.todo.findById(todo.id);
      assert(result.id === todo.id);
      assert(result.title === todo.title);
    });

    it('should return null for non-existent id', async () => {
      const result = await ctx.service.todo.findById(999);
      assert(result === null);
    });
  });

  describe('create()', () => {
    it('should create todo successfully', async () => {
      const todoData = {
        title: '测试任务',
        description: '测试描述',
        priority: 2,
      };
      
      const result = await ctx.service.todo.create(todoData);
      assert(result.title === todoData.title);
      assert(result.description === todoData.description);
      assert(result.priority === todoData.priority);
      assert(result.completed === false);
    });

    it('should create todo with default priority', async () => {
      const todoData = { title: '测试任务' };
      const result = await ctx.service.todo.create(todoData);
      assert(result.priority === 1);
    });
  });

  describe('update()', () => {
    it('should update todo successfully', async () => {
      const todo = await app.factory.create('todo');
      const updateData = {
        title: '更新后的标题',
        completed: true,
      };
      
      const result = await ctx.service.todo.update(todo.id, updateData);
      assert(result.title === updateData.title);
      assert(result.completed === updateData.completed);
    });

    it('should return null for non-existent todo', async () => {
      const result = await ctx.service.todo.update(999, { title: '测试' });
      assert(result === null);
    });

    it('should filter out disallowed fields', async () => {
      const todo = await app.factory.create('todo');
      const updateData = {
        title: '更新后的标题',
        id: 999, // 不允许更新的字段
        created_at: new Date(), // 不允许更新的字段
      };
      
      const result = await ctx.service.todo.update(todo.id, updateData);
      assert(result.title === updateData.title);
      assert(result.id === todo.id); // ID 不应该被更新
    });
  });

  describe('delete()', () => {
    it('should delete todo successfully', async () => {
      const todo = await app.factory.create('todo');
      const result = await ctx.service.todo.delete(todo.id);
      assert(result === true);
      
      const deletedTodo = await ctx.service.todo.findById(todo.id);
      assert(deletedTodo === null);
    });

    it('should return false for non-existent todo', async () => {
      const result = await ctx.service.todo.delete(999);
      assert(result === false);
    });
  });

  describe('toggleComplete()', () => {
    it('should toggle completion status', async () => {
      const todo = await app.factory.create('todo', { completed: false });
      const result = await ctx.service.todo.toggleComplete(todo.id);
      assert(result.completed === true);
      
      const toggledAgain = await ctx.service.todo.toggleComplete(todo.id);
      assert(toggledAgain.completed === false);
    });

    it('should return null for non-existent todo', async () => {
      const result = await ctx.service.todo.toggleComplete(999);
      assert(result === null);
    });
  });

  describe('getOverdue()', () => {
    it('should return overdue todos', async () => {
      await app.factory.create('overdueTodo');
      await app.factory.create('todo'); // 未过期的任务
      
      const result = await ctx.service.todo.getOverdue();
      assert(result.length === 1);
      assert(result[0].due_date < new Date());
      assert(result[0].completed === false);
    });
  });

  describe('batchUpdateStatus()', () => {
    it('should update multiple todos status', async () => {
      const todo1 = await app.factory.create('todo');
      const todo2 = await app.factory.create('todo');
      
      const affectedRows = await ctx.service.todo.batchUpdateStatus([todo1.id, todo2.id], true);
      assert(affectedRows === 2);
      
      const updatedTodo1 = await ctx.service.todo.findById(todo1.id);
      const updatedTodo2 = await ctx.service.todo.findById(todo2.id);
      assert(updatedTodo1.completed === true);
      assert(updatedTodo2.completed === true);
    });

    it('should throw error for empty ids array', async () => {
      try {
        await ctx.service.todo.batchUpdateStatus([], true);
        assert.fail('Should throw error');
      } catch (error) {
        assert(error.message === '任务ID列表不能为空');
      }
    });
  });

  describe('search()', () => {
    it('should search todos by title and description', async () => {
      await app.factory.create('todo', { title: '学习 JavaScript', description: '学习前端开发' });
      await app.factory.create('todo', { title: '购买书籍', description: '买一本 JavaScript 教程' });
      await app.factory.create('todo', { title: '运动', description: '去健身房锻炼' });
      
      const result = await ctx.service.todo.search('JavaScript');
      assert(result.list.length === 2);
      assert(result.keyword === 'JavaScript');
    });

    it('should return all todos for empty keyword', async () => {
      await app.factory.createMany('todo', 3);
      
      const result = await ctx.service.todo.search('');
      assert(result.list.length === 3);
    });
  });

  describe('getStatistics()', () => {
    it('should return correct statistics', async () => {
      await app.factory.create('todo', { priority: 1 });
      await app.factory.create('todo', { priority: 2 });
      await app.factory.create('completedTodo', { priority: 3 });
      await app.factory.create('overdueTodo');
      
      const stats = await ctx.service.todo.getStatistics();
      assert(stats.total === 4);
      assert(stats.completed === 1);
      assert(stats.incomplete === 3);
      assert(stats.overdue === 1);
      assert(stats.priorityStats[1] === 1);
      assert(stats.priorityStats[2] === 2); // 包括过期任务
    });
  });
});
