'use strict';

const { app, assert } = require('egg-mock/bootstrap');

describe('test/app/controller/todo.test.js', () => {
  beforeEach(async () => {
    // 清理测试数据
    await app.model.Todo.destroy({ where: {}, force: true });
  });

  afterEach(async () => {
    // 清理测试数据
    await app.model.Todo.destroy({ where: {}, force: true });
  });

  describe('GET /api/todos', () => {
    it('should return empty list when no todos', async () => {
      const result = await app.httpRequest()
        .get('/api/todos')
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.data.list.length === 0);
      assert(result.body.data.total === 0);
    });

    it('should return todos list with pagination', async () => {
      await app.factory.createMany('todo', 15);
      
      const result = await app.httpRequest()
        .get('/api/todos?page=1&limit=10')
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.data.list.length === 10);
      assert(result.body.data.total === 15);
      assert(result.body.data.totalPages === 2);
    });

    it('should filter by completed status', async () => {
      await app.factory.create('todo');
      await app.factory.create('completedTodo');
      
      const result = await app.httpRequest()
        .get('/api/todos?completed=true')
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.data.list.length === 1);
      assert(result.body.data.list[0].completed === true);
    });
  });

  describe('GET /api/todos/:id', () => {
    it('should return todo by id', async () => {
      const todo = await app.factory.create('todo');
      
      const result = await app.httpRequest()
        .get(`/api/todos/${todo.id}`)
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.data.id === todo.id);
      assert(result.body.data.title === todo.title);
    });

    it('should return 404 for non-existent todo', async () => {
      const result = await app.httpRequest()
        .get('/api/todos/999')
        .expect(404);
      
      assert(result.body.success === false);
      assert(result.body.message === '待办事项不存在');
    });

    it('should return 400 for invalid id', async () => {
      const result = await app.httpRequest()
        .get('/api/todos/invalid')
        .expect(400);
      
      assert(result.body.success === false);
      assert(result.body.message === '无效的待办事项ID');
    });
  });

  describe('POST /api/todos', () => {
    it('should create todo successfully', async () => {
      const todoData = {
        title: '测试任务',
        description: '测试描述',
        priority: 2,
      };
      
      const result = await app.httpRequest()
        .post('/api/todos')
        .send(todoData)
        .expect(201);
      
      assert(result.body.success === true);
      assert(result.body.data.title === todoData.title);
      assert(result.body.data.description === todoData.description);
      assert(result.body.data.priority === todoData.priority);
      assert(result.body.data.completed === false);
    });

    it('should return 400 for missing title', async () => {
      const todoData = {
        description: '测试描述',
      };
      
      const result = await app.httpRequest()
        .post('/api/todos')
        .send(todoData)
        .expect(400);
      
      assert(result.body.success === false);
      assert(result.body.message === '参数验证失败');
    });

    it('should return 400 for invalid priority', async () => {
      const todoData = {
        title: '测试任务',
        priority: 5, // 无效的优先级
      };
      
      const result = await app.httpRequest()
        .post('/api/todos')
        .send(todoData)
        .expect(400);
      
      assert(result.body.success === false);
    });
  });

  describe('PUT /api/todos/:id', () => {
    it('should update todo successfully', async () => {
      const todo = await app.factory.create('todo');
      const updateData = {
        title: '更新后的标题',
        completed: true,
      };
      
      const result = await app.httpRequest()
        .put(`/api/todos/${todo.id}`)
        .send(updateData)
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.data.title === updateData.title);
      assert(result.body.data.completed === updateData.completed);
    });

    it('should return 404 for non-existent todo', async () => {
      const updateData = { title: '更新标题' };
      
      const result = await app.httpRequest()
        .put('/api/todos/999')
        .send(updateData)
        .expect(404);
      
      assert(result.body.success === false);
      assert(result.body.message === '待办事项不存在');
    });
  });

  describe('DELETE /api/todos/:id', () => {
    it('should delete todo successfully', async () => {
      const todo = await app.factory.create('todo');
      
      const result = await app.httpRequest()
        .delete(`/api/todos/${todo.id}`)
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.message === '删除待办事项成功');
    });

    it('should return 404 for non-existent todo', async () => {
      const result = await app.httpRequest()
        .delete('/api/todos/999')
        .expect(404);
      
      assert(result.body.success === false);
      assert(result.body.message === '待办事项不存在');
    });
  });

  describe('PATCH /api/todos/:id/toggle', () => {
    it('should toggle completion status', async () => {
      const todo = await app.factory.create('todo', { completed: false });
      
      const result = await app.httpRequest()
        .patch(`/api/todos/${todo.id}/toggle`)
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.data.completed === true);
    });

    it('should return 404 for non-existent todo', async () => {
      const result = await app.httpRequest()
        .patch('/api/todos/999/toggle')
        .expect(404);
      
      assert(result.body.success === false);
      assert(result.body.message === '待办事项不存在');
    });
  });

  describe('GET /api/todos/overdue/list', () => {
    it('should return overdue todos', async () => {
      await app.factory.create('overdueTodo');
      await app.factory.create('todo'); // 未过期的任务
      
      const result = await app.httpRequest()
        .get('/api/todos/overdue/list')
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.data.length === 1);
    });
  });

  describe('GET /api/todos/statistics/summary', () => {
    it('should return statistics', async () => {
      await app.factory.create('todo');
      await app.factory.create('completedTodo');
      await app.factory.create('overdueTodo');
      
      const result = await app.httpRequest()
        .get('/api/todos/statistics/summary')
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.data.total === 3);
      assert(result.body.data.completed === 1);
      assert(result.body.data.incomplete === 2);
      assert(result.body.data.overdue === 1);
    });
  });

  describe('GET /api/todos/search/query', () => {
    it('should search todos', async () => {
      await app.factory.create('todo', { title: '学习 JavaScript' });
      await app.factory.create('todo', { title: '购买书籍', description: 'JavaScript 教程' });
      await app.factory.create('todo', { title: '运动' });
      
      const result = await app.httpRequest()
        .get('/api/todos/search/query?keyword=JavaScript')
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.data.list.length === 2);
      assert(result.body.data.keyword === 'JavaScript');
    });
  });

  describe('PATCH /api/todos/batch/status', () => {
    it('should batch update status', async () => {
      const todo1 = await app.factory.create('todo');
      const todo2 = await app.factory.create('todo');
      
      const result = await app.httpRequest()
        .patch('/api/todos/batch/status')
        .send({
          ids: [todo1.id, todo2.id],
          completed: true,
        })
        .expect(200);
      
      assert(result.body.success === true);
      assert(result.body.data.affectedRows === 2);
    });

    it('should return 400 for invalid request', async () => {
      const result = await app.httpRequest()
        .patch('/api/todos/batch/status')
        .send({
          ids: [],
          completed: true,
        })
        .expect(500);
      
      assert(result.body.success === false);
    });
  });
});
