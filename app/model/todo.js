'use strict';

module.exports = app => {
  const { STRING, INTEGER, BOOLEAN, DATE } = app.Sequelize;

  const Todo = app.model.define('Todo', {
    id: {
      type: INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '主键ID',
    },
    title: {
      type: STRING(255),
      allowNull: false,
      comment: '任务标题',
      validate: {
        notEmpty: {
          msg: '任务标题不能为空',
        },
        len: {
          args: [1, 255],
          msg: '任务标题长度必须在1-255个字符之间',
        },
      },
    },
    description: {
      type: STRING(1000),
      allowNull: true,
      comment: '任务描述',
      validate: {
        len: {
          args: [0, 1000],
          msg: '任务描述长度不能超过1000个字符',
        },
      },
    },
    completed: {
      type: BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否完成',
    },
    priority: {
      type: INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '优先级：1-低，2-中，3-高',
      validate: {
        isIn: {
          args: [[1, 2, 3]],
          msg: '优先级必须是1（低）、2（中）或3（高）',
        },
      },
    },
    due_date: {
      type: DATE,
      allowNull: true,
      comment: '截止日期',
      validate: {
        isDate: {
          msg: '截止日期格式不正确',
        },
      },
    },
    created_at: {
      type: DATE,
      allowNull: false,
      defaultValue: app.Sequelize.NOW,
      comment: '创建时间',
    },
    updated_at: {
      type: DATE,
      allowNull: false,
      defaultValue: app.Sequelize.NOW,
      comment: '更新时间',
    },
  }, {
    tableName: 'todos',
    timestamps: true,
    underscored: true,
    comment: '待办事项表',
    indexes: [
      {
        name: 'idx_completed',
        fields: ['completed'],
      },
      {
        name: 'idx_priority',
        fields: ['priority'],
      },
      {
        name: 'idx_due_date',
        fields: ['due_date'],
      },
      {
        name: 'idx_created_at',
        fields: ['created_at'],
      },
    ],
  });

  // 实例方法：切换完成状态
  Todo.prototype.toggleComplete = function() {
    this.completed = !this.completed;
    return this.save();
  };

  // 实例方法：检查是否过期
  Todo.prototype.isOverdue = function() {
    if (!this.due_date) return false;
    return new Date() > new Date(this.due_date) && !this.completed;
  };

  // 类方法：获取未完成的任务
  Todo.getIncomplete = function() {
    return this.findAll({
      where: { completed: false },
      order: [['priority', 'DESC'], ['created_at', 'ASC']],
    });
  };

  // 类方法：获取已完成的任务
  Todo.getCompleted = function() {
    return this.findAll({
      where: { completed: true },
      order: [['updated_at', 'DESC']],
    });
  };

  // 类方法：根据优先级获取任务
  Todo.getByPriority = function(priority) {
    return this.findAll({
      where: { priority },
      order: [['created_at', 'ASC']],
    });
  };

  // 类方法：获取过期任务
  Todo.getOverdue = function() {
    const now = new Date();
    return this.findAll({
      where: {
        due_date: {
          [app.Sequelize.Op.lt]: now,
        },
        completed: false,
      },
      order: [['due_date', 'ASC']],
    });
  };

  return Todo;
};
