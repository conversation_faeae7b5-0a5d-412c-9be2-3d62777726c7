'use strict';

const Service = require('egg').Service;

class TodoService extends Service {
  // 获取待办事项列表（支持分页和筛选）
  async list(options = {}) {
    const { page = 1, limit = 10, completed, priority } = options;
    const offset = (page - 1) * limit;
    
    const where = {};
    if (completed !== undefined) {
      where.completed = completed;
    }
    if (priority !== undefined) {
      where.priority = priority;
    }

    const { count, rows } = await this.ctx.model.Todo.findAndCountAll({
      where,
      limit,
      offset,
      order: [
        ['priority', 'DESC'],
        ['created_at', 'ASC'],
      ],
    });

    return {
      list: rows,
      total: count,
      page,
      limit,
      totalPages: Math.ceil(count / limit),
    };
  }

  // 根据ID查找待办事项
  async findById(id) {
    return await this.ctx.model.Todo.findByPk(id);
  }

  // 创建待办事项
  async create(todoData) {
    const { title, description, priority = 1, due_date } = todoData;
    
    return await this.ctx.model.Todo.create({
      title,
      description,
      priority,
      due_date,
      completed: false,
    });
  }

  // 更新待办事项
  async update(id, updateData) {
    const todo = await this.ctx.model.Todo.findByPk(id);
    if (!todo) {
      return null;
    }

    // 过滤掉不允许更新的字段
    const allowedFields = ['title', 'description', 'completed', 'priority', 'due_date'];
    const filteredData = {};
    
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }

    await todo.update(filteredData);
    return todo;
  }

  // 删除待办事项
  async delete(id) {
    const todo = await this.ctx.model.Todo.findByPk(id);
    if (!todo) {
      return false;
    }

    await todo.destroy();
    return true;
  }

  // 切换完成状态
  async toggleComplete(id) {
    const todo = await this.ctx.model.Todo.findByPk(id);
    if (!todo) {
      return null;
    }

    await todo.toggleComplete();
    return todo;
  }

  // 获取未完成的任务
  async getIncomplete() {
    return await this.ctx.model.Todo.getIncomplete();
  }

  // 获取已完成的任务
  async getCompleted() {
    return await this.ctx.model.Todo.getCompleted();
  }

  // 根据优先级获取任务
  async getByPriority(priority) {
    return await this.ctx.model.Todo.getByPriority(priority);
  }

  // 获取过期任务
  async getOverdue() {
    return await this.ctx.model.Todo.getOverdue();
  }

  // 批量更新任务状态
  async batchUpdateStatus(ids, completed) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('任务ID列表不能为空');
    }

    const result = await this.ctx.model.Todo.update(
      { completed },
      {
        where: {
          id: {
            [this.ctx.app.Sequelize.Op.in]: ids,
          },
        },
      }
    );

    return result[0]; // 返回受影响的行数
  }

  // 批量删除任务
  async batchDelete(ids) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('任务ID列表不能为空');
    }

    const result = await this.ctx.model.Todo.destroy({
      where: {
        id: {
          [this.ctx.app.Sequelize.Op.in]: ids,
        },
      },
    });

    return result; // 返回删除的行数
  }

  // 获取统计信息
  async getStatistics() {
    const total = await this.ctx.model.Todo.count();
    const completed = await this.ctx.model.Todo.count({
      where: { completed: true },
    });
    const incomplete = total - completed;
    
    const overdue = await this.ctx.model.Todo.count({
      where: {
        due_date: {
          [this.ctx.app.Sequelize.Op.lt]: new Date(),
        },
        completed: false,
      },
    });

    const priorityStats = await this.ctx.model.Todo.findAll({
      attributes: [
        'priority',
        [this.ctx.app.Sequelize.fn('COUNT', this.ctx.app.Sequelize.col('id')), 'count'],
      ],
      where: { completed: false },
      group: ['priority'],
      raw: true,
    });

    return {
      total,
      completed,
      incomplete,
      overdue,
      priorityStats: priorityStats.reduce((acc, item) => {
        acc[item.priority] = parseInt(item.count);
        return acc;
      }, {}),
    };
  }

  // 搜索任务
  async search(keyword, options = {}) {
    const { page = 1, limit = 10 } = options;
    const offset = (page - 1) * limit;

    if (!keyword || keyword.trim() === '') {
      return this.list({ page, limit });
    }

    const { count, rows } = await this.ctx.model.Todo.findAndCountAll({
      where: {
        [this.ctx.app.Sequelize.Op.or]: [
          {
            title: {
              [this.ctx.app.Sequelize.Op.like]: `%${keyword}%`,
            },
          },
          {
            description: {
              [this.ctx.app.Sequelize.Op.like]: `%${keyword}%`,
            },
          },
        ],
      },
      limit,
      offset,
      order: [
        ['priority', 'DESC'],
        ['created_at', 'ASC'],
      ],
    });

    return {
      list: rows,
      total: count,
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      keyword,
    };
  }
}

module.exports = TodoService;
