'use strict';

const Controller = require('egg').Controller;

class TodoController extends Controller {
  // 获取所有待办事项
  async index() {
    console.log('111 :>> ', 111);
    const { ctx } = this;
    try {
      const { page = 1, limit = 10, completed, priority } = ctx.query;
      const result = await ctx.service.todo.list({
        page: parseInt(page),
        limit: parseInt(limit),
        completed: completed !== undefined ? completed === 'true' : undefined,
        priority: priority ? parseInt(priority) : undefined,
      });
      
      ctx.body = {
        success: true,
        data: result,
        message: '获取待办事项列表成功',
      };
    } catch (error) {
      ctx.logger.error('获取待办事项列表失败:', error);
      ctx.body = {
        success: false,
        message: '获取待办事项列表失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 获取单个待办事项
  async show() {
    const { ctx } = this;
    try {
      const id = parseInt(ctx.params.id);
      if (!id || id <= 0) {
        ctx.body = {
          success: false,
          message: '无效的待办事项ID',
        };
        ctx.status = 400;
        return;
      }

      const todo = await ctx.service.todo.findById(id);
      if (!todo) {
        ctx.body = {
          success: false,
          message: '待办事项不存在',
        };
        ctx.status = 404;
        return;
      }

      ctx.body = {
        success: true,
        data: todo,
        message: '获取待办事项成功',
      };
    } catch (error) {
      ctx.logger.error('获取待办事项失败:', error);
      ctx.body = {
        success: false,
        message: '获取待办事项失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 创建待办事项
  async create() {
    const { ctx } = this;
    try {
      // 参数验证
      ctx.validate({
        title: { type: 'string', required: true, min: 1, max: 255 },
        description: { type: 'string', required: false, max: 1000 },
        priority: { type: 'int', required: false, min: 1, max: 3 },
        due_date: { type: 'date', required: false },
      });

      const todoData = ctx.request.body;
      const todo = await ctx.service.todo.create(todoData);

      ctx.body = {
        success: true,
        data: todo,
        message: '创建待办事项成功',
      };
      ctx.status = 201;
    } catch (error) {
      ctx.logger.error('创建待办事项失败:', error);
      if (error.name === 'ValidationError') {
        ctx.body = {
          success: false,
          message: '参数验证失败',
          errors: error.errors,
        };
        ctx.status = 400;
      } else {
        ctx.body = {
          success: false,
          message: '创建待办事项失败',
          error: error.message,
        };
        ctx.status = 500;
      }
    }
  }

  // 更新待办事项
  async update() {
    const { ctx } = this;
    try {
      const id = parseInt(ctx.params.id);
      if (!id || id <= 0) {
        ctx.body = {
          success: false,
          message: '无效的待办事项ID',
        };
        ctx.status = 400;
        return;
      }

      // 参数验证
      ctx.validate({
        title: { type: 'string', required: false, min: 1, max: 255 },
        description: { type: 'string', required: false, max: 1000 },
        completed: { type: 'boolean', required: false },
        priority: { type: 'int', required: false, min: 1, max: 3 },
        due_date: { type: 'date', required: false },
      });

      const updateData = ctx.request.body;
      const todo = await ctx.service.todo.update(id, updateData);

      if (!todo) {
        ctx.body = {
          success: false,
          message: '待办事项不存在',
        };
        ctx.status = 404;
        return;
      }

      ctx.body = {
        success: true,
        data: todo,
        message: '更新待办事项成功',
      };
    } catch (error) {
      ctx.logger.error('更新待办事项失败:', error);
      if (error.name === 'ValidationError') {
        ctx.body = {
          success: false,
          message: '参数验证失败',
          errors: error.errors,
        };
        ctx.status = 400;
      } else {
        ctx.body = {
          success: false,
          message: '更新待办事项失败',
          error: error.message,
        };
        ctx.status = 500;
      }
    }
  }

  // 删除待办事项
  async destroy() {
    const { ctx } = this;
    try {
      const id = parseInt(ctx.params.id);
      if (!id || id <= 0) {
        ctx.body = {
          success: false,
          message: '无效的待办事项ID',
        };
        ctx.status = 400;
        return;
      }

      const result = await ctx.service.todo.delete(id);
      if (!result) {
        ctx.body = {
          success: false,
          message: '待办事项不存在',
        };
        ctx.status = 404;
        return;
      }

      ctx.body = {
        success: true,
        message: '删除待办事项成功',
      };
    } catch (error) {
      ctx.logger.error('删除待办事项失败:', error);
      ctx.body = {
        success: false,
        message: '删除待办事项失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 切换完成状态
  async toggle() {
    const { ctx } = this;
    try {
      const id = parseInt(ctx.params.id);
      if (!id || id <= 0) {
        ctx.body = {
          success: false,
          message: '无效的待办事项ID',
        };
        ctx.status = 400;
        return;
      }

      const todo = await ctx.service.todo.toggleComplete(id);
      if (!todo) {
        ctx.body = {
          success: false,
          message: '待办事项不存在',
        };
        ctx.status = 404;
        return;
      }

      ctx.body = {
        success: true,
        data: todo,
        message: '切换完成状态成功',
      };
    } catch (error) {
      ctx.logger.error('切换完成状态失败:', error);
      ctx.body = {
        success: false,
        message: '切换完成状态失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 获取过期任务
  async overdue() {
    const { ctx } = this;
    try {
      const todos = await ctx.service.todo.getOverdue();
      ctx.body = {
        success: true,
        data: todos,
        message: '获取过期任务成功',
      };
    } catch (error) {
      ctx.logger.error('获取过期任务失败:', error);
      ctx.body = {
        success: false,
        message: '获取过期任务失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 批量更新状态
  async batchUpdateStatus() {
    const { ctx } = this;
    try {
      ctx.validate({
        ids: { type: 'array', required: true, itemType: 'int' },
        completed: { type: 'boolean', required: true },
      });

      const { ids, completed } = ctx.request.body;
      const affectedRows = await ctx.service.todo.batchUpdateStatus(ids, completed);

      ctx.body = {
        success: true,
        data: { affectedRows },
        message: `批量${completed ? '完成' : '取消完成'}任务成功`,
      };
    } catch (error) {
      ctx.logger.error('批量更新状态失败:', error);
      ctx.body = {
        success: false,
        message: '批量更新状态失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 批量删除
  async batchDelete() {
    const { ctx } = this;
    try {
      ctx.validate({
        ids: { type: 'array', required: true, itemType: 'int' },
      });

      const { ids } = ctx.request.body;
      const deletedRows = await ctx.service.todo.batchDelete(ids);

      ctx.body = {
        success: true,
        data: { deletedRows },
        message: '批量删除任务成功',
      };
    } catch (error) {
      ctx.logger.error('批量删除失败:', error);
      ctx.body = {
        success: false,
        message: '批量删除失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 获取统计信息
  async statistics() {
    const { ctx } = this;
    try {
      const stats = await ctx.service.todo.getStatistics();
      ctx.body = {
        success: true,
        data: stats,
        message: '获取统计信息成功',
      };
    } catch (error) {
      ctx.logger.error('获取统计信息失败:', error);
      ctx.body = {
        success: false,
        message: '获取统计信息失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 搜索任务
  async search() {
    const { ctx } = this;
    try {
      const { keyword, page = 1, limit = 10 } = ctx.query;
      const result = await ctx.service.todo.search(keyword, {
        page: parseInt(page),
        limit: parseInt(limit),
      });

      ctx.body = {
        success: true,
        data: result,
        message: '搜索任务成功',
      };
    } catch (error) {
      ctx.logger.error('搜索任务失败:', error);
      ctx.body = {
        success: false,
        message: '搜索任务失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 获取未完成任务
  async incomplete() {
    const { ctx } = this;
    try {
      const todos = await ctx.service.todo.getIncomplete();
      ctx.body = {
        success: true,
        data: todos,
        message: '获取未完成任务成功',
      };
    } catch (error) {
      ctx.logger.error('获取未完成任务失败:', error);
      ctx.body = {
        success: false,
        message: '获取未完成任务失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 获取已完成任务
  async completed() {
    const { ctx } = this;
    try {
      const todos = await ctx.service.todo.getCompleted();
      ctx.body = {
        success: true,
        data: todos,
        message: '获取已完成任务成功',
      };
    } catch (error) {
      ctx.logger.error('获取已完成任务失败:', error);
      ctx.body = {
        success: false,
        message: '获取已完成任务失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }

  // 根据优先级获取任务
  async byPriority() {
    const { ctx } = this;
    try {
      const priority = parseInt(ctx.params.priority);
      if (!priority || priority < 1 || priority > 3) {
        ctx.body = {
          success: false,
          message: '无效的优先级，必须是1（低）、2（中）或3（高）',
        };
        ctx.status = 400;
        return;
      }

      const todos = await ctx.service.todo.getByPriority(priority);
      ctx.body = {
        success: true,
        data: todos,
        message: '获取指定优先级任务成功',
      };
    } catch (error) {
      ctx.logger.error('获取指定优先级任务失败:', error);
      ctx.body = {
        success: false,
        message: '获取指定优先级任务失败',
        error: error.message,
      };
      ctx.status = 500;
    }
  }
}

module.exports = TodoController;
