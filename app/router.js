'use strict';

/**
 * @param {Egg.Application} app - egg application
 */
module.exports = app => {
  const { router, controller } = app;

  // 首页路由
  router.get('/', controller.home.index);

  // Todo API 路由
  // RESTful API 设计
  router.resources('todos', '/api/todos', controller.todo);
  
  // 额外的自定义路由
  // 切换完成状态
  router.patch('/api/todos/:id/toggle', controller.todo.toggle);
  
  // 获取过期任务
  router.get('/api/todos/overdue/list', controller.todo.overdue);
  
  // 批量操作路由
  router.patch('/api/todos/batch/status', controller.todo.batchUpdateStatus);
  router.delete('/api/todos/batch/delete', controller.todo.batchDelete);
  
  // 统计信息
  router.get('/api/todos/statistics/summary', controller.todo.statistics);
  
  // 搜索任务
  router.get('/api/todos/search/query', controller.todo.search);
  
  // 根据状态获取任务
  router.get('/api/todos/status/incomplete', controller.todo.incomplete);
  router.get('/api/todos/status/completed', controller.todo.completed);
  
  // 根据优先级获取任务
  router.get('/api/todos/priority/:priority', controller.todo.byPriority);
};
