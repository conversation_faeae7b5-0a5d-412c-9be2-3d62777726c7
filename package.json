{"name": "example", "version": "1.0.0", "description": "", "dependencies": {"egg": "2.10.0", "egg-cors": "^3.0.1", "egg-scripts": "^2.5.0", "egg-sequelize": "^4.0.2", "egg-validate": "^2.0.2", "mysql2": "^3.14.2"}, "devDependencies": {"autod": "^3.0.1", "autod-egg": "^1.0.0", "egg-bin": "^4.3.7", "egg-mock": "^3.19.2", "eslint": "^4.18.1", "eslint-config-egg": "^7.0.0", "factory-girl": "^5.0.2", "sequelize-cli": "^4.0.0"}, "engines": {"node": ">=8.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-example", "stop": "egg-scripts stop --title=egg-server-example", "dev": "egg-bin dev", "debug": "egg-bin debug", "autod": "autod", "lint": "eslint .", "test": "egg-bin test", "cov": "egg-bin cov", "ci": "npm run lint && NODE_ENV=test npx sequelize db:migrate && npm run cov"}, "ci": {"version": "8"}, "eslintIgnore": ["coverage", "dist"], "repository": {"type": "git", "url": ""}, "files": ["lib", "index.js"], "author": "", "private": true}