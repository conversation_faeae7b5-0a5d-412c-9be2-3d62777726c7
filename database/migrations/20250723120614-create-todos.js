'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('todos', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '主键ID',
      },
      title: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: '任务标题',
      },
      description: {
        type: Sequelize.STRING(1000),
        allowNull: true,
        comment: '任务描述',
      },
      completed: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否完成',
      },
      priority: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '优先级：1-低，2-中，3-高',
      },
      due_date: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '截止日期',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: '创建时间',
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: '更新时间',
      },
    }, {
      comment: '待办事项表',
    }).then(() => {
      // 创建索引
      return Promise.all([
        queryInterface.addIndex('todos', ['completed'], {
          name: 'idx_completed',
        }),
        queryInterface.addIndex('todos', ['priority'], {
          name: 'idx_priority',
        }),
        queryInterface.addIndex('todos', ['due_date'], {
          name: 'idx_due_date',
        }),
        queryInterface.addIndex('todos', ['created_at'], {
          name: 'idx_created_at',
        }),
      ]);
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('todos');
  },
};
